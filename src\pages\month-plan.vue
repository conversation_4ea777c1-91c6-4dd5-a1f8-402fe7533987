<template>
  <div class="main" v-loading="ui.loading">
    <div class="search-area" :update-code="formData.searchAreaUpdateCode">
      <table>
        <tr>
          <template v-for="searchAreaLeftItem in formData.searchAreaLeftList">
            <td class="label">
              {{searchAreaLeftItem.label}}
            </td>
            <td class="body">
              <el-select v-model="formData.query[searchAreaLeftItem.name]" placeholder="请选择" size="small"
                         :style="searchAreaLeftItem.style" filterable
                         :disabled="searchAreaLeftItem.disabled"
                         @change="handleSearchAreaFieldSelect($event, findOption($event, searchAreaLeftItem.items), searchAreaLeftItem)">
                <el-option
                    v-for="item in searchAreaLeftItem.items"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </td>
          </template>
          <td class="label" v-if="formData.useYearMonthSelector">
            月份：
          </td>
          <td class="body" v-if="formData.useYearMonthSelector">
            <!-- 月份选择器 -->
            <el-date-picker
                v-model="formData.yearMonth"
                type="month" value-format="yyyy-MM"
                placeholder="选择月" size="small" style="width: 120px;">
            </el-date-picker>
          </td>
          <td class="label" v-if="formData.useDateSelector">
            日期：
          </td>
          <td class="body" v-if="formData.useDateSelector">
            <!-- 日期选择器 -->
            <el-date-picker
                v-model="formData.date"
                type="date" value-format="yyyy-MM-dd"
                placeholder="选择日期" size="small" style="width: 135px;">
            </el-date-picker>
          </td>
          <td class="label" v-if="formData.useSearchButton">
            &nbsp;
          </td>
          <td class="body" v-if="formData.useSearchButton">
            <!-- 查询按钮 -->
            <el-button type="primary" size="small" icon="el-icon-search" @click="handleSearchClick">查询</el-button>
          </td>
          <template v-for="searchAreaButtonRightItem in formData.searchAreaButtonRightList">
            <td class="label">
              &nbsp;
            </td>
            <td class="body">
              <el-button :type="searchAreaButtonRightItem.theme" size="small" :icon="searchAreaButtonRightItem.icon"
                         @click="handleSearchAreaButtonRightItemClick(searchAreaButtonRightItem.name, searchAreaButtonRightItem)"
                         style="height: 32.5px;">
                {{searchAreaButtonRightItem.label}}
              </el-button>
            </td>
          </template>
        </tr>
      </table>
    </div>
    <div style="border-top: 1px solid #efefef;width: 90%;margin:auto;margin-top: 5px;margin-bottom: 30px;"></div>
    <div class="month-area" :update-code="formData.monthAreaUpdateCode">
      <table>
        <tr class="header-row">
          <template v-for="weekDay in formData.weekDays">
            <td>{{weekDay.label}}</td>
          </template>
        </tr>
        <template v-for="monthDayRow in formData.monthDayRows">
          <tr>
            <template v-for="monthDay in monthDayRow.items">
              <td :class="{
                'month-cell': true,
                'cur-month-cell': (monthDay.name.indexOf(formData.yearMonth) !== -1),
                'oth-month-cell': (monthDay.name.indexOf(formData.yearMonth) === -1) }">
                <template v-if="formData.monthDayCellType === 0">
                  <div style="height: 12px;"></div>
                  <span class="month-cell-label">{{getMonthDayCellDateLabel(monthDay)}}</span>
                  <div style="height: 10px;"></div>
                  <div style="display: flex; flex-direction: row; line-height: 30px;">
                    <div style="border-right: 1px solid rgba(232,232,232,0.35);width: 50%; font-size: 13px;">
                      <span :style="getMonthDayCellFlag0LabelStyle(monthDay)">{{getMonthDayCellFlag0Label(monthDay)}}</span>
                    </div>
                    <div style="flex-grow: 1;">
                      <el-button size="small" @click="handleMonthDayCellClick(monthDay)"
                                 :disabled="isMonthDayCellButtonDisabled(monthDay)">
                        {{ getMonthDayCellButtonLabel(monthDay) }}
                      </el-button>
                    </div>
                  </div>
                  <div style="height: 12px;"></div>
                </template>
                <template v-else-if="formData.monthDayCellType === 1">
                  <div style="text-align: right;background-color: #f6f6f6;line-height: 25px;padding-right: 6px;">
                    <span style="font-size: 13px;font-weight: bold;">{{getMonthDayCellDateLabel(monthDay)}}日</span>
                  </div>
                  <div style="min-height: 100px;">
                    <template v-for="item in getMonthDayCellDataList(monthDay)">
                      <div style="line-height: 25px;font-size: 13px;
                      overflow:hidden; text-overflow:ellipsis; white-space:nowrap;max-width: 165px;margin:auto;">
                        <span :title="item.label">{{item.label}}</span>
                      </div>
                    </template>
                  </div>
                  <div style="height: 12px;"></div>
                </template>
              </td>
            </template>
          </tr>
        </template>
      </table>
    </div>
    <div style="height: 20px;"></div>
  </div>
</template>

<script>
const ctx = {
  conn: null,
};

export default {
  watch: {
    'formData.yearMonth': function (newVal) {
      // console.log(`年月更变：${newVal}`);
      if (newVal && newVal.length > 0) {
        if (this.formData.date.indexOf(newVal) === -1) {
          const arr = newVal.split('-');
          this.formData.date = `${arr[0]}-${arr[1]}-01`;
        }
      }
      else {
        this.formData.date = '';
      }

      this.loadData();
    },
    'formData.date': function(newVal) {
      // console.log(`日期更变：${newVal}`);
      if (newVal && newVal.length > 0) {
        const arr = newVal.split('-');
        this.formData.yearMonth = `${arr[0]}-${arr[1]}`;
      }
      else {
        this.formData.yearMonth = '';
      }
    },
  },
  data() {
    const self = this;
    return {
      formData: {
        debug: false,

        /*** 可配置部分 ***/
        // 周类型：0 周一在第一个，1 周日在第一个
        weekType: 0,
        // 0 参考员工订餐，1 参考请假情况
        monthDayCellType: 0,

        // 月日单元0号标签
        monthDayCellFlag0Label: '未定',
        // 月日单元0号标签样式
        monthDayCellFlag0LabelStyle: null,
        // 月日单元按钮标签
        monthDayCellButtonLabel: '点击',

        // 使用年月选择器
        useYearMonthSelector: true,
        // 使用日期选择器
        useDateSelector: true,
        // 使用查询按钮
        useSearchButton: true,

        // 查询区左边自定义字段列表
        searchAreaLeftList: [
          // {
          //   label: '科室：', type: 'select',
          //   name: 'deptId',
          //   items: [
          //     { label: '财务科', value: '1' },
          //     { label: '办公室', value: '2' },
          //     { label: '安保科', value: '3' },
          //   ],
          //   onSelect(value, option, selector) {
          //   },
          // }
        ],
        // 查询区右边自定义按钮列表
        searchAreaButtonRightList: [
          // {
          //   label: 'XX统计',
          //   name: 'xxtj',
          //   theme: 'info',
          //   onClick() {
          //   },
          // }
        ],

        // 设置加载状态
        setLoading(val) {
          self.ui.loading = val;
        },
        // 加载自定义数据
        async loadCustomData() {},
        // 当月日单元按钮点击
        async onMonthDayCellClick(date, hasMonthDayData) {},
        // 是否禁用月日单元按钮
        isMonthDayCellButtonDisabled(date) {},
        /**
         * 获取月日单元数据列表
         * @param date
         * @returns {*[]} [{ label }]
         */
        getMonthDayCellDataList(date) {
          return [];
        },

        /*** 可调用部分 ***/
        searchAreaUpdateCode: 0,
        monthAreaUpdateCode: 0,
        loadData: async function() {
          await self.loadData();
        },

        /*** 系统部分 ***/
        query: {},
        // 年月
        yearMonth: '',
        // 日期
        date: '',

        // 一周中的天
        weekDays: [], // { label: '周一', value: 1 }

        monthDayRows: [], // { items: [{ name: '2021-01-01' }] }
        // 月份中天数组
        monthDays: [], // { name: '2021-01-01' }

        // 业务数据
        monthDayDataList: [
          // { name: '2024-11-23', label: '已订餐', labelStyle: '', button: { label: '撤回' } }
        ],
      },
      ui: {
        loading: true,
        bindMonthDayCurIndex: null,
      }
    };
  },
  methods: {
    isDebug() {
      return this.formData.debug;
    },
    async onShowed() {
      const self = this;
      self.formData.yearMonth = self.getNowYearMonth();
      self.formData.date = self.getNowDate();

      // 应用触发器设计
      if (ctx.conn.invokeComp4.triggerDesign) {
        const triggerDesign = ctx.conn.invokeComp4.triggerDesign;

        if (triggerDesign.formPage.cols && triggerDesign.formPage.cols.length > 0) {

          for (const col of triggerDesign.formPage.cols) {

            if (col.type === 'afterLoadForm') {
              eval(col.code)({
                ...ctx.conn,
                formData: self.formData,
              });
            }
          }
        }
      }

      // await self.loadData();
    },
    async loadData() {
      if (this.isDebug()) console.log(`加载数据...`);
      const self = this;

      self.ui.loading = true;

      await self.formData.loadCustomData();
      this.loadWeekDays();
      this.loadMonthDays();

      self.ui.loading = false;
    },
    loadWeekDays() {
      if (this.formData.weekType === 0) {
        this.formData.weekDays = [
          { label: '周一', value: 1 },
          { label: '周二', value: 2 },
          { label: '周三', value: 3 },
          { label: '周四', value: 4 },
          { label: '周五', value: 5 },
          { label: '周六', value: 6 },
          { label: '周日', value: 0 },
        ];
      }
      else if (this.formData.weekType === 1) {
        this.formData.weekDays = [
          { label: '周日', value: 0 },
          { label: '周一', value: 1 },
          { label: '周二', value: 2 },
          { label: '周三', value: 3 },
          { label: '周四', value: 4 },
          { label: '周五', value: 5 },
          { label: '周六', value: 6 },
        ];
      }
      else {
        this.formData.weekDays = []
      }
    },
    loadMonthDays() {
      const self = this;
      this.formData.monthDayRows = [];
      this.formData.monthDays = [];

      // 确定月份第一天是周几
      const firstDayOfMonth = self.formData.yearMonth + '-01';
      const firstDayOfMonthDate = new Date(firstDayOfMonth);
      const firstDayOfWeek = firstDayOfMonthDate.getDay();
      let lastDayOfMonth = null;

      // 周一为一周的第一天
      if (self.formData.weekType === 0) {
        // 填充上个月天数
        if (firstDayOfWeek !== 1) {
          // 如果是周日
          if (firstDayOfWeek === 0) {
            let date = new Date(firstDayOfMonth);
            for (let i = 0; i < 6; i++) {
              date.setDate(date.getDate() - 1);
              // self.formData.monthDays.push({ name: self.toDateStr(date) });
              self.formData.monthDays.splice(0, null, { name: self.toDateStr(date) });
            }
          }
          else {
            let date = new Date(firstDayOfMonth);
            for (let i = 0; i < firstDayOfWeek - 1; i++) {
              date.setDate(date.getDate() - 1);
              // self.formData.monthDays.push({ name: self.toDateStr(date) });
              self.formData.monthDays.splice(0, null, { name: self.toDateStr(date) });
            }
          }
        }
        // 填充本月天数
        {
          let date = new Date(firstDayOfMonth);
          for (let i = 0; i < 50; i++) {
            self.formData.monthDays.push({ name: self.toDateStr(date) });
            if (self.isLastDate(date)) {
              lastDayOfMonth = date;
              break;
            }
            else {
              date.setDate(date.getDate() + 1);
            }
          }
        }
        // 填充下个月天数
        if (lastDayOfMonth && lastDayOfMonth.getDay() !== 0) {
          let date = new Date(lastDayOfMonth);
          for (let i = 0; i < 7 - lastDayOfMonth.getDay(); i++) {
            date.setDate(date.getDate() + 1);
            self.formData.monthDays.push({ name: self.toDateStr(date) });
          }
        }

        // 转为 monthDayRows
        let monthDayRow = [];
        for (let i = 0; i < self.formData.monthDays.length; i++) {
          monthDayRow.push(self.formData.monthDays[i]);
          if ((i + 1) % 7 === 0) {
            self.formData.monthDayRows.push({ items: monthDayRow });
            monthDayRow = [];
          }
        }
      }
      // 周日为一周的第一天
      else if (self.formData.weekType === 1) {

      }

      // console.log(this.formData.monthDays);
      // console.log(this.formData.monthDayRows);
    },

    findOption(value, options) {
      for (const option of options) {
        if (option.value === value) {
          return option;
        }
      }
    },
    /**
     * 获取月份日单元日标签
     * @param item
     * @returns {string}
     */
    getMonthDayCellDateLabel(item) {
      const arr = item.name.split('-');
      return `${arr[2]}`;
    },
    getMonthDayCellFlag0Label(item) {
      for (const monthDayData of this.formData.monthDayDataList) {
        if (monthDayData.name === item.name) {
          if (monthDayData.label && monthDayData.label.length > 0) {
            return monthDayData.label;
          }
        }
      }
      return this.formData.monthDayCellFlag0Label;
    },
    getMonthDayCellFlag0LabelStyle(item) {
      for (const monthDayData of this.formData.monthDayDataList) {
        if (monthDayData.name === item.name) {
          if (monthDayData.labelStyle && monthDayData.labelStyle.length > 0) {
            return monthDayData.labelStyle;
          }
        }
      }
      return this.formData.monthDayCellFlag0LabelStyle;
    },
    getMonthDayCellButtonLabel(item) {
      for (const monthDayData of this.formData.monthDayDataList) {
        if (monthDayData.name === item.name) {
          if (monthDayData.button) {
            return monthDayData.button.label;
          }
        }
      }
      return this.formData.monthDayCellButtonLabel;
    },
    getMonthDayCellDataList(item) {
      return this.formData.getMonthDayCellDataList(item.name);
    },
    /**
     * 获取当前年月（注意：月份是两位数），例：2023-01
     */
    getNowYearMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      return `${year}-${month < 10 ? '0' + month : month}`;
    },
    /**
     * 获取当前日期，例：2023-01-12
     * 注意：月份是两位数，日是两位数
     */
    getNowDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
    },
    /**
     * 把日期转为字符串，例：2023-01-12
     * @param date {Date}
     */
    toDateStr(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
    },
    /**
     * 判定是否为月份最后一天
     * @param date {Date}
     */
    isLastDate(date) {
      return date.getDate() === new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
    },
    isMonthDayCellButtonDisabled(monthDay) {
      if (this.formData.isMonthDayCellButtonDisabled) {
        return this.formData.isMonthDayCellButtonDisabled(monthDay.name);
      }
    },
    handleSearchClick() {
      this.loadData();
    },
    handleSearchAreaFieldSelect(value, option, selector) {
      if (selector.onSelect) {
        selector.onSelect(value, option, selector);
      }
      this.formData.searchAreaUpdateCode++;
    },
    async handleMonthDayCellClick(item) {
      if (this.formData.onMonthDayCellClick) {
        let hasMonthDayData = false;

        for (const monthDayData of this.formData.monthDayDataList) {
          if (monthDayData.name === item.name) {
            hasMonthDayData = true;
            break;
          }
        }

        await this.formData.onMonthDayCellClick(item.name, hasMonthDayData);
      }
    },
    handleSearchAreaButtonRightItemClick(name, item) {
      if (item.onClick) {
        item.onClick();
      }
    },
  },
  created() {
    const self = this;
    window.connect = function(conn) {
      // console.log('startDesign')
      // console.log(conn.invokeComp4.startDesign);

      // console.log('triggerDesign')
      // console.log(conn.invokeComp4.triggerDesign);

      ctx.conn = conn;
      self.onShowed();

      return true;
    };
  },
  mounted() {
    const self = this;
    if (process.env.NODE_ENV === 'development') {
      ctx.conn = {
        invokeComp4: {}
      };
      self.onShowed();
    }
  }
}
</script>

<style scoped lang="less">
.main {

  .search-area {
    text-align: center;
    line-height: 60px;
    text-align: center;

    table {
      margin: auto;
    }

    .label {
      text-align: right;
      padding-left: 20px;
      font-size: 13px;
    }

    .body {

    }
  }

  .month-area {

    table {
      margin: auto;
      border-collapse: collapse;
      border: 1px solid #e1e1e1;
    }

    td {
      border: 1px solid #e1e1e1;
      width: 180px;
      text-align: center;
    }

    .header-row {

      td {
        line-height: 30px;
      }
    }

    .month-cell {
      padding: 0 0 0 0;
    }

    .cur-month-cell {

    }

    .oth-month-cell {
      color: silver;
    }

    .month-cell-label {
      font-size: 30px;
    }
  }

}
</style>