<template>
  <div class="attendance-report">
    <div class="report-header">
      <h2>2025年5月考勤报表</h2>
    </div>

    <div class="table-container">
      <!-- 整个表格的滚动容器 -->
      <div class="table-scroll-container">
        <!-- 固定表头 -->
        <div class="fixed-header">
          <div class="header-left">
            <div class="header-cell dept-col">部门</div>
            <div class="header-cell name-col">姓名</div>
            <div class="header-cell type-col">类型</div>
          </div>
          <div class="header-right">
            <div class="header-cell date-col" v-for="day in daysInMonth" :key="day">
              {{ String(day).padStart(2, '0') }}
            </div>
          </div>
        </div>

        <!-- 表格主体 -->
        <div class="table-body">
          <div class="table-row" v-for="(row, rowIndex) in tableData" :key="rowIndex">
            <!-- 固定左侧列 -->
            <div class="row-left">
              <div class="cell dept-col" :class="{ 'dept-header': row.isDeptHeader }">
                {{ row.isDeptHeader ? row.department : '' }}
              </div>
              <div class="cell name-col">{{ row.name || '' }}</div>
              <div class="cell type-col">{{ row.type || '' }}</div>
            </div>

            <!-- 数据区域 -->
            <div class="row-right">
              <div
                class="cell date-col"
                v-for="day in daysInMonth"
                :key="day"
                :class="getDateCellClass(row, day)"
              >
                {{ getDateCellContent(row, day) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* 考勤月报表 */
export default {
  data() {
    return {
      // 部门和人员
      departments: [
        {
          name: '财务部', users: [
            {id: 'xiaoming', name: '小明'},
            {id: 'xiaohong', name: '小红'},
          ]
        },
        {
          name: '信息科技部', users: [
            {id: 'xiaogang', name: '小刚'},
            {id: 'xiaoli', name: '小李'},
          ]
        },
        {
          name: '市场部', users: [
            {id: 'wangwu', name: '王五'},
            {id: 'zhaoliu', name: '赵六'},
          ]
        },
      ],
      // 考勤签到签退数据
      signInOutData: [
        {date: '2025-05-01', time: '08:31', userId: 'xiaoming'},
        {date: '2025-05-01', time: '08:32', userId: 'xiaohong'},
        {date: '2025-05-02', time: '08:14', userId: 'xiaogang'},
        {date: '2025-05-02', time: '08:16', userId: 'xiaoli'},
        {date: '2025-05-03', time: '08:42', userId: 'xiaoming'},
        {date: '2025-05-03', time: '08:44', userId: 'xiaohong'},
        {date: '2025-05-04', time: '08:26', userId: 'xiaogang'},
        {date: '2025-05-04', time: '08:40', userId: 'xiaoli'},
        {date: '2025-05-05', time: '08:32', userId: 'wangwu'},
        {date: '2025-05-05', time: '08:35', userId: 'zhaoliu'},
      ],
      // 请假申请数据
      leaveApplications: [
        {
          startDate: '2025-05-06',
          startTime: 'am',
          endDate: '2025-05-06',
          endTime: 'pm',
          userId: 'xiaoming',
          reason: '病假'
        },
        {
          startDate: '2025-05-07',
          startTime: 'am',
          endDate: '2025-05-07',
          endTime: 'pm',
          userId: 'xiaohong',
          reason: '事假'
        },
      ],
    }
  },
  computed: {
    // 生成当月的天数
    daysInMonth() {
      const days = []
      for (let i = 1; i <= 31; i++) {
        days.push(i)
      }
      return days
    },

    // 生成表格数据
    tableData() {
      const data = []

      this.departments.forEach(dept => {
        // 部门标题行
        data.push({
          isDeptHeader: true,
          department: dept.name,
          name: '',
          type: ''
        })

        // 每个用户的上下班记录
        dept.users.forEach(user => {
          // 上班记录
          data.push({
            isDeptHeader: false,
            department: '',
            name: user.name,
            type: '上',
            userId: user.id,
            recordType: 'in'
          })

          // 下班记录
          data.push({
            isDeptHeader: false,
            department: '',
            name: '',
            type: '下',
            userId: user.id,
            recordType: 'out'
          })
        })
      })

      return data
    }
  },
  methods: {
    // 获取日期单元格的内容
    getDateCellContent(row, day) {
      if (row.isDeptHeader) return ''

      const dateStr = `2025-05-${String(day).padStart(2, '0')}`

      // 检查请假记录
      const leave = this.leaveApplications.find(app =>
        app.userId === row.userId &&
        app.startDate <= dateStr &&
        app.endDate >= dateStr
      )

      if (leave) {
        return leave.reason === '病假' ? '病假' : '事假'
      }

      // 检查签到记录
      const signRecord = this.signInOutData.find(record =>
        record.userId === row.userId &&
        record.date === dateStr
      )

      if (signRecord) {
        return signRecord.time
      }

      // 周末显示
      const date = new Date(2025, 4, day) // 月份从0开始
      const dayOfWeek = date.getDay()
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        return '√'
      }

      return ''
    },

    // 获取日期单元格的样式类
    getDateCellClass(row, day) {
      if (row.isDeptHeader) return ''

      const dateStr = `2025-05-${String(day).padStart(2, '0')}`
      const date = new Date(2025, 4, day)
      const dayOfWeek = date.getDay()

      const classes = []

      // 周末样式
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        classes.push('weekend')
      }

      // 请假样式
      const leave = this.leaveApplications.find(app =>
        app.userId === row.userId &&
        app.startDate <= dateStr &&
        app.endDate >= dateStr
      )

      if (leave) {
        classes.push('leave')
      }

      // 迟到样式（8:30后算迟到）
      const signRecord = this.signInOutData.find(record =>
        record.userId === row.userId &&
        record.date === dateStr
      )

      if (signRecord && row.recordType === 'in') {
        const time = signRecord.time.replace(':', '')
        if (parseInt(time) > 830) {
          classes.push('late')
        }
      }

      return classes.join(' ')
    }
  }
}
</script>

<style scoped>
.attendance-report {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.report-header {
  text-align: center;
  margin-bottom: 20px;
}

.report-header h2 {
  color: #333;
  font-size: 24px;
  margin: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* 表格滚动容器 */
.table-scroll-container {
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
}

/* 固定表头 */
.fixed-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 2px solid #e0e0e0;
  display: flex;
  min-width: fit-content;
}

.header-left {
  display: flex;
  position: sticky;
  left: 0;
  z-index: 101;
  background: white;
  border-right: 2px solid #e0e0e0;
}

.header-right {
  display: flex;
}

.header-cell {
  padding: 12px 8px;
  font-weight: bold;
  text-align: center;
  border-right: 1px solid #e0e0e0;
  background: #f8f9fa;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dept-col {
  width: 100px;
  min-width: 100px;
}

.name-col {
  width: 80px;
  min-width: 80px;
}

.type-col {
  width: 40px;
  min-width: 40px;
}

.date-col {
  width: 50px;
  min-width: 50px;
}

/* 表格主体 */
.table-body {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  min-width: fit-content;
}

.table-row:hover {
  background-color: #f0f8ff;
}

.row-left {
  display: flex;
  position: sticky;
  left: 0;
  z-index: 10;
  background: white;
  border-right: 2px solid #e0e0e0;
}

.row-right {
  display: flex;
}

.cell {
  padding: 8px;
  border-right: 1px solid #e0e0e0;
  text-align: center;
  font-size: 12px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
}

/* 部门标题行样式 */
.dept-header {
  background-color: #fff3cd;
  font-weight: bold;
  color: #856404;
}

/* 周末样式 */
.weekend {
  background-color: #e8f5e8;
  color: #2e7d32;
}

/* 请假样式 */
.leave {
  background-color: #ffebee;
  color: #c62828;
  font-weight: bold;
}

/* 迟到样式 */
.late {
  background-color: #fff3e0;
  color: #ef6c00;
  font-weight: bold;
}

/* 滚动条样式 */
.header-right::-webkit-scrollbar,
.row-right::-webkit-scrollbar,
.table-body::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.header-right::-webkit-scrollbar-track,
.row-right::-webkit-scrollbar-track,
.table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.header-right::-webkit-scrollbar-thumb,
.row-right::-webkit-scrollbar-thumb,
.table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.header-right::-webkit-scrollbar-thumb:hover,
.row-right::-webkit-scrollbar-thumb:hover,
.table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance-report {
    padding: 10px;
  }

  .dept-col {
    width: 80px;
    min-width: 80px;
  }

  .name-col {
    width: 60px;
    min-width: 60px;
  }

  .type-col {
    width: 30px;
    min-width: 30px;
  }

  .date-col {
    width: 40px;
    min-width: 40px;
  }

  .cell {
    font-size: 11px;
    padding: 6px 4px;
  }
}
</style>