import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import vue2 from '@vitejs/plugin-vue2';
// import { createVuePlugin } from 'vite-plugin-vue2'
// import babel from '@rollup/plugin-babel';

export default({ mode }) => {
  const { VITE_PORT, VITE_BASE_URL } = loadEnv(mode, process.cwd());

  return defineConfig({
    base: './',
    plugins: [
      vue2(),
      // createVuePlugin(),
      // babel({
      //   babelHelpers: 'bundled',
      //   exclude: 'node_modules/**', // 确保排除 node_modules
      // }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname,'src')
      }
    }
  });
}