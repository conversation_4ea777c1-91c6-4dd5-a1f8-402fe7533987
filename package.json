{"name": "vue2-vite-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "test": "cross-env NODE_ENV=test vite --mode test", "build": "vite build", "build-copy": "dev-build-copy-to.bat", "preview": "vite preview"}, "devDependencies": {"@types/node": "^20.5.7", "@vitejs/plugin-vue2": "^2.2.0", "@rollup/plugin-babel": "^6.0.3", "babel-plugin-component": "^1.1.1", "less": "^4.2.0", "less-loader": "^11.1.3", "vite": "^4.4.0"}, "dependencies": {"cross-env": "^7.0.3", "element-ui": "^2.15.13", "pinia": "^2.1.4", "vue": "^2.7.14", "vue-router": "^3.6.5", "vue-template-compiler": "^2.7.14"}}