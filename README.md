# 项目模板
vue2 + vite + pinia
</br></br>

## 使用.env
App.vue的内容
```javascript
/* 在App.vue加入代码 */
import Vue from "vue";
Vue.prototype.$env = import.meta.env;

// 其他地方这么使用就行了
Vue.prototype.$env.MODE // 返回当前环境：development、production
Vue.prototype.$env.VITE_PORT
```
.env中的内容
```gitignore
# 项目本地运行端口号
VITE_PORT = 3000

# 打包路径
VITE_BASE_URL = ./
```
</br></br>

## 使用element-ui
安装
```cmd
npm i element-ui -S
```
在main.js加入
```javascript
// 加入element-ui
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
Vue.use(ElementUI);
```
按需加载-安装
- 参考：https://element.eleme.cn/#/zh-CN/component/quickstart
```cmd
npm install babel-plugin-component -D
```
在.babelrc写入
```javascript
{
  "presets": [],
  "plugins": [
    [
      "component",
      {
        "libraryName": "element-ui",
        "styleLibraryName": "theme-chalk"
      }
    ]
  ]
}
```
在vite.config.js
```javascript
import babel from '@rollup/plugin-babel';

export default {
  base: './',
  plugins: [
    babel({
      babelHelpers: 'bundled',
      exclude: 'node_modules/**', // 确保排除 node_modules
    }),
  ]
}
```
按需使用组件
```javascript

import Vue from "vue";
import { Button } from 'element-ui';

export default {
  components: {
    'el-button': Button
  },
}
```
或
```javascript
import Vue from 'vue';
import { Button, Select } from 'element-ui';
import App from './App.vue';

Vue.component(Button.name, Button);
Vue.component(Select.name, Select);
/* 或写为
 * Vue.use(Button)
 * Vue.use(Select)
 */

new Vue({
  el: '#app',
  render: h => h(App)
});
```

## 使用less
```
cnpm install less less-loader --save-dev
```