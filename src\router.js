import Vue from 'vue'
import VueRouter from 'vue-router'

// 注册路由插件
Vue.use(VueRouter)

// 
const routes = [
  // {
  //   path: '/',
  //   name: 'Home',
  //   component: () => import('./pages/home.vue')
  // },
  {
    path: '/month-plan',
    name: 'month-plan',
    component: () => import('./pages/month-plan.vue')
  },
  {
    path: '/kaoqin-month-report',
    name: 'kaoqin-month-report',
    component: () => import('./pages/kaoqin-month-report.vue')
  },
]

const router = new VueRouter({
  routes
})

export default router